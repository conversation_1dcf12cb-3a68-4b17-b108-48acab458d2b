.task_content {
  /deep/.el-select {
    width: 100%;
  }
  /deep/.el-textarea__inner {
    font-family: "Roboto-Regular", sans-serif;
  }
  /deep/.el-form-item {
    margin-bottom: 13px;

    .el-form-item__content {
      text-align: left;
    }
  }
  /deep/.el-date-editor.el-input,
  /deep/.el-date-editor.el-input__inner {
    width: 100%;
  }
  .task-title {
    margin: 0;
    line-height: 20px;
    color: #38425b;
    font-size: 14px;
  }

  .task-btn {
    text-align: right;
    .el-button {
      background-color: #678993;
      color: #fff;
      border: none;
      width: 100px;
      font-family: "Roboto-Medium", sans-serif;
      font-size: 14px;
    }

    .clear-btn {
      background-color: #e2eff6;
      color: #38425b;
    }
  }
}
.el-tooltip__popper.is-light{
    background: #678993;
    border-color: #678993;
    color: #fff;
}
