// $color1
@import "./init.scss";
.primary-button,
.block-button,
.canary-yellow-button,
.light-blue-button {
  font-family: 'Roboto-Bold';
  font-size: 14px;
}

.primary-button {
  color: #fff;
  background-color: $color1;
  border-color: $color1;
  &:hover {
    background-color: $color3;
    color: #fff;
  }

  &:active {
    background-color: $color3;
    color: #fff;
  }

  &:disabled {
    background-color: $color2;
    color: #999;
    border-color: $color2;
  }
}

//#fff
.block-button {
  background-color: #fff;
  color: $color1;
  border-color: $color1;
  &:hover {
    background-color: $color3;
    color: #fff;
  }
  &:active {
    background-color: $color3;
    color: #fff;
  }
  &:disabled {
    background-color: $color2;
    color: #999;
    border-color: $color2;
  }
}
// #E2EFF6
.blue-button{
  color: $color4;
  background-color: #E2EFF6;
  border-color: #E2EFF6;
}

// #E2EFF6
.light-blue-button {
  color: $color4;
  background-color: #E2EFF6;
  border-color: #E2EFF6;
  &:hover {
    background-color: $color3;
    color: #fff;
  }

  &:active {
    background-color: $color3;
    color: #fff;
  }

  &:disabled {
    background-color: $color2;
    color: #999;
    border-color: $color2;
  }
}

.form-group-element{


  .el-switch__label *,
  .el-radio__label{
    font-size:14px !important;
    color: #2C3E50 !important;
    line-height: 20px;
  }
  
  .el-radio__input.is-checked .el-radio__inner{
    border-color: #1C4A5E;
    background: #fff;
  }
  
  .el-radio__inner{
    width: 18px;
    height: 18px;
  }
  
  .el-radio__inner::after{
    width: 10px;
    height: 10px;
    background: #1C4A5E;
  }
}

.main-dialog .el-dialog{
  width:40%;
}

.main-dialog .el-dialog__header{
  text-align: left;
  color: #092A35;
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  padding-bottom: 0px;
  letter-spacing: 1px;
}

.main-dialog .el-dialog__body{
  padding-top: 5px;
}

.component-divider{
  width: 60%;
}

@media only screen and (max-width: 768px) {
.main-dialog .el-dialog{
  width:100%;
}

.sort-photo-modal{
  /deep/ .el-dialog--center{
    width:80%;
  }
} 
}

@media only screen and (min-width: 768px) and (max-width: 1200px) {
.main-dialog .el-dialog{
  width:60%;
}
.component-divider{
  width: 100%;
}
}


.el-input__inner::-webkit-inner-spin-button,
  .el-input__inner::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

.el-input__inner {
  appearance: none;
  -moz-appearance: textfield; /* Firefox */
}
  
.sort-photo-modal{
  /deep/ .el-dialog__body{
    border-top: 1px solid #dcdfe6;
    margin-top: 25px;
    padding:20px 20px 0px;
  }
  /deep/ .el-dialog__footer{
    text-align: left;
  }
}

.ckedit-note .ck-editor__editable {
  min-height: 150px;
 }
