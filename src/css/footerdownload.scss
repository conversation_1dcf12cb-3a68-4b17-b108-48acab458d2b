.footer-download {
    position: fixed;
    bottom: 0;
    width: 100%;
    left: 0;
    z-index: 90;
    background-color: #fff;
    padding: 10px 0;
    box-shadow: 0 -10px 20px 0 rgba(17, 61, 71, 0.1);
    .row {
      justify-content: center;
    }
    /deep/ .el-radio-button {
      margin-right: 30px;
      .el-radio-button__inner {
        border: 1px solid #678993;
        border-radius: 4px;
        color: #678993;
        width: 200px;
        font-family: "Roboto-Regular", sans-serif;
        &:hover {
          background: #678993;
          color: #fff;
        }
      }
      .el-radio-button__orig-radio:checked + .el-radio-button__inner {
        box-shadow: 0 0 0.036364rem 0.036364rem #678993;
      }
    }
    /deep/ .el-radio-button.is-active .el-radio-button__inner,
    /deep/ .el-radio-button.is-focus .el-radio-button__inner {
      background: #678993;
      color: #fff;
    }
    @media (max-width:992px) {
      display: none;
    }
  }