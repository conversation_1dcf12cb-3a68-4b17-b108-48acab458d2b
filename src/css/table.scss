.table {
  font-family: "Roboto-Medium", sans-serif;
  /deep/ .el-table {
    .become_angry {
      background-color: #e1e7e9;
    }
    .cell {
      word-wrap: break-word;
      word-break: break-word;
    }
    th {
    //   background-color: #eff3f4;
    //   border: none;
      >.cell {
        word-wrap: break-word;
        word-break: break-word;
      }
    }

    td {
      font-size: 16px;
      font-family: "Roboto-Regular", sans-serif;
      padding: 0;

      &:hover {
        cursor: pointer;
      }
    }

    .el-table--enable-row-hover .el-table__body tr:hover>td {
      background-color: #e1e7e9;
      cursor: pointer;
    }
  }
  
  ::-webkit-scrollbar {
    width: 6px;
    /*滚动条宽度*/
    height: 8px;
    /*滚动条高度*/
    background-color: white;
  }
  /*定义滑块 内阴影+圆角*/
  ::-webkit-scrollbar-thumb {
    -webkit-box-shadow: inset 0 0 0px white;
    background-color: rgb(193, 193, 193);
    /*滚动条的背景颜色*/
    border-radius: 30px;
  }

  /* 对上下滚动条的单独修改 */
  .el-table__body-wrapper::-webkit-scrollbar {
    /*width: 0;宽度为0隐藏*/
    width: 0px;
  }
}
