.lease-detail {
    .row {
      justify-content: space-between;
    }
    .move{display: none;}
    .lease-form {
      width: 100%;
      /deep/ .el-form-item__label::before {
        display: none;
      }
     
      .broke-row {
        justify-content: space-between;
        align-items: flex-start;
        .broke-sheck {
          width: 240px;
          text-align: left;
          /deep/ .el-radio__input {
            width: 10px;
            height: 10px;
            background-color: #f4f9fc;
            .el-radio__inner {
              border-radius: 2px;
              background-color: #f4f9fc;
            }
          }
          /deep/ .el-radio__input.is-checked .el-radio__inner {
            border: none;
            background: none;
            background-image: url("../../assets/icon/ico-checkbox.svg");
            background-size: cover;
          }
          /deep/ .el-radio__input.is-checked + .el-radio__label {
            color: #606266;
          }
          /deep/ .el-radio__inner::after {
            display: none;
          }
        }
        .broke-input {
          width: calc(100% - 260px);
          .input-row {
            align-items: flex-start;
            .broke-text {
              text-align: right;
              display: inline-block;
              width: 160px;
              color: #38425b;
              font-size: 14px;
            }
            /deep/ .el-select {
              width: calc(100% - 180px);
            }
          }
          /deep/ .el-form-item__error {
            left: 180px;
          }
        }
      }
    .infor-row {
      justify-content: space-between; 
      align-items: flex-start;
      -webkit-flex-direction: row-reverse;
      flex-direction: row-reverse;
      .form-left,
      .form-right {
        width: calc(calc(100% - 40px) / 2);
        /deep/ .el-form-item {
          margin-bottom: 17px;
        }
        .date-item {
          
          /deep/ .el-form-item {
            width: calc(calc(100% - 20px) / 2);
            .el-input {
              width: 100%;
            }
          }
        }
      }
      .form-right {
        .check-group-button {
          width: 100%;
          display: flex;
          justify-content: flex-start;
          display: -webkit-flex;
          -webkit-flex-wrap: wrap;
          flex-wrap: wrap;
          align-items: center;
          /deep/ .el-checkbox-button {
            margin: 0px 20px 0px 0;
            border-radius: 4px;
            .el-checkbox-button__inner {
              border: none;
              background: #f4f9fc;
              border-radius: 4px;
              &:hover {
                color: #333;
              }
            }
          }
          /deep/ .el-checkbox-button.is-checked .el-checkbox-button__inner {
            background-color: #678993;
            box-shadow: -0.017857rem 0 0 0 #678993;
            &:hover {
              color: #fff;
            }
          }
        }
        .textarea-tip {
          margin: 0;
          text-align: left;
          font-family: "Roboto-Italic", sans-serif;
          font-size: 12px;
          color: #36425d;
        }
      }
     }
    }
    .btn-sumit {
      text-align: right;
      position: relative;
      margin-top: 110px;
      padding: 20px 20px 0 0;
      .el-button {
        padding: 0 20px;
        .row {
          justify-content: space-between;
          width: 100px;
          align-items: center;
          i {
            font-size: 18px;
          }
        }
      }
      &::after {
        content: "";
        border: 1px solid #e1e9ef;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
      }
    }
    /deep/ .el-form-item__label {
      font-size: 16px;
    }
    /* 修改 el-checkbox 样式 */
    /deep/ .el-checkbox__input.is-checked .el-checkbox__inner,
    /deep/ .el-checkbox__input.is-indeterminate .el-checkbox__inner {
      // border-color: #678993;
      // background-color: #678993;
      border: none;
      background: none;
      background-image: url("../../assets/icon/ico-checkbox.svg");
      &::after {
        display: none;
      }
    }
    /deep/ .el-checkbox__input.is-focus .el-checkbox__inner,
    /deep/ .el-checkbox__input.is-focus:hover {
      border-color: #678993;
    }
    /deep/ .el-checkbox__input.is-checked + .el-checkbox__label {
      color: #333;
    }
    /*在谷歌下移除input[number]的上下箭头*/
    /deep/ input[type="number"]::-webkit-outer-spin-button,
    /deep/ input[type="number"]::-webkit-inner-spin-button {
      -webkit-appearance: none !important;
      margin: 0;
    }
    /*在firefox下移除input[number]的上下箭头*/
    /deep/ input[type="number"] {
      -moz-appearance: textfield;
    }
    .other-dialog{
      /deep/ .el-dialog{ width: 500px;}
    }
    @media (max-width:992px) {
      .move{display: block;}
      .lease-form {
        .broke-row{display: block;.broke-sheck, .broke-input{width: 100%; .input-row {justify-content: flex-start;.broke-text {text-align: left;width: 130px;}/deep/.el-select{width: calc(100% - 130px);}}}}
        .infor-row{
        display: block;
        .form-left,.form-right{width: 100%;
          .date-item {display: flex;}
        }}
      }
      .pc,.lease-form .infor-row .form-left .pc.date-item{display: none;}
      .other-dialog{
        /deep/ .el-dialog{ width: 90%;}
      }
    }
  }
  /* 头部的信息 */
  .detail-top {
    .detail-top-info {
      padding: 20px 0 10px 0;
      font-family: "Roboto-Bold", sans-serif;
      margin: 0;
      font-size: 18px;
      text-align: left;
      color: #36425d;
    }
  }
  /* 表单水平部分 */
  .form-horizontal {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  
    .lease-info-horizontal {
      display: flex;
      flex-wrap: wrap;
  
      /deep/ .el-form-item {
        margin-right: 0px;
      }
    }
    .cancel {
      font-size: 16px;
      color: red;
      line-height: 98px;
  
      :hover {
        cursor: pointer;
      }
    }
  }
  
  /* 表单垂直部分 */
  .form-vertical {
    // min-width: 400px;
    background-color: #fff;
    // padding: 30px;
    display: flex;
    flex-direction: column;
    .other_fee {
      /deep/ .el-form-item__content {
        // display: flex;
        // justify-content: flex-start;
        // align-items: center;
      }
      /* 修改 Manage Other Fees 样式 */
      .manage-other-fees {
        color: #72949d;
        cursor: pointer;
        margin: 0;
        a {
          text-decoration: underline;
        }
      }
      @media (max-width: 650px) {
        /deep/ .el-form-item__content {
          display: block;
        }
        .manage-other-fees {
          margin-left: 0;
        }
      }
    }
    .form-item-textarea /deep/ .el-form-item__label {
      line-height: 20px;
    }
    /deep/ .el-form-item {
      display: flex;
      margin-bottom: 2px;
      .el-form-item__label {
        width: 30%;
        min-width: 120px;
        padding: 0;
      }
      .el-form-item__content {
        width: calc(100% - 140px);
        text-align: left;
        label {
          font-size: 16px;
        }
        .el-input__inner {
          // max-width: 240px;
          width: 100%;
          padding-left: 45px;
        }
      }
      .el-input__prefix {
        left: 1px;
        width: 35px;
        top: 1.5px;
        height: calc(100% - 4px);
        background-color: #f4f9fc;
      }
    }
    .total {
      font-weight: 600;
      /deep/ .el-form-item__label {
        font-size: 20px;
      }
      /deep/ .el-form-item__content {
        label {
          font-size: 20px;
        }
      }
    }
  
    .form-item-upload {
      .form-item-box {
        border: 1px Dashed #678993;
        text-align: center;
        background-color: #f4f9fc;
        width: 100%;
        justify-content: center;
        align-items: center;
        padding: 10px 0;
        .upload-demo {
          /deep/ .el-button {
            background-color: #f4f9fc;
            border: none;
            padding-left: 0;
          }
        }
        &:hover {
          cursor: pointer;
        }
      }
      /deep/ .el-form-item__label {
        visibility: hidden;
      }
      /deep/ .el-form-item__content {
        display: flex;
      }
    }
  }
  
  /deep/ .el-dialog {
    border-radius: 10px;
    .el-dialog__header {
      text-align: left;
      height: 30px;
      // background-color: #57737b;
      .el-dialog__title {
        color: #404b66;
        font-size: 22px;
        font-family: "Roboto-Bold", sans-serif;
      }
      .el-dialog__headerbtn .el-dialog__close {
        color: #678993;
        font-size: 22px;
        font-weight: 600;
      }
    }
  }
  
  /* 其他费用对话框 */
  .other-fee-dialog {
    .other-fee-add {
      text-align: left;
      /deep/ .el-button {
        border: none;
        padding-left: 0;
        background-color: #fff;
        a {
          color: #72949d;
          text-decoration: underline;
        }
        &:hover,
        &:focus {
          color: #333;
        }
      }
      /deep/ .el-button.is-disabled {
        color: #c0c4cc;
      }
    }
    .other-fee-check {
      text-align: left;
    }
    .other-fee-detail {
      display: flex;
      justify-content: space-between;
      .other-fee-delete {
        i {
          margin-top: 30px;
          font-weight: 600;
          font-size: 18px;
          color: #678993;
          cursor: pointer;
        }
      }
      /deep/ .el-form-item {
        margin-bottom: 0;
        .el-form-item__label {
          line-height: 20px;
        }
        .el-input--prefix .el-input__inner {
          padding-left: 40px;
        }
        .el-input__prefix {
          left: 1px;
          width: 35px;
          top: 1.5px;
          height: calc(100% - 3px);
          background-color: #f4f9fc;
        }
      }
      .fee {
        width: 140px;
      }
      .reason {
        width: calc(100% - 200px);
      }
    }
  
    .other-fee-total {
      display: flex;
      justify-content: space-between;
    }
  }