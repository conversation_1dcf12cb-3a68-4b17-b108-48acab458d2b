.payment-top {
    padding: 0 20px;
    /* 头部信息 */
    .top_bax {
      background-color: #fff;
      padding: 0px 0px 40px 0px;
      .title {
        font-family: "Roboto-Bold", sans-serif;
        margin: 20px 0 0 0;
        font-size: 20px;
        text-align: left;
        color: #36425d;
      }
    }
  
    // 表格
    .pay_content {
      border-radius: 10px;
      background-color: #fff;
      /deep/ input::-webkit-outer-spin-button,
      /deep/ input::-webkit-inner-spin-button {
        -webkit-appearance: none;
      }
      /deep/ input[type="number"] {
        -moz-appearance: textfield;
      }
      .pay_title {
        height: 60px;
        font-family: "Roboto-Bold", sans-serif;
        justify-content: space-between;
        span {
          margin-right: 20px;
          font-size: 18px;
        }
      }
    }
    .cnacel_type_top {
      text-align: left;
      font-size: 20px;
      font-family: "Roboto-Medium", sans-serif;
      margin: 0 0 30px 0;
    }
    .btn {
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }
  
  /*  */
  .payment-bottom {
    justify-content: space-between;
    position: relative;
    margin-top: 0px;
    padding: 20px 20px 0 20px;
    .el-button {
      padding: 0 20px;
      .row {
        justify-content: space-between;
        width: 100px;
        align-items: center;
        i {
          font-size: 18px;
        }
      }
    }
    &::after {
      content: "";
      border: 1px solid #e1e9ef;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
    }
  }
  input {
    outline: none;
  }
  
  .payment_box {
    font-family: "Roboto-Regular", sans-serif;
    padding-bottom: 100px;
  }
  
  .payment_box .pay_btn /deep/ .el-button {
    background: none;
    border: none;
    font-size: 16px;
    color: red;
  }
  
  .pay_select {
    position: relative;
    padding: 10px 0;
    .text {
      font-size: 14px;
    }
    .pay_selectleft {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin: 10px 0;
      /deep/ .el-select {
        width: 100px;
        margin-left: 20px;
      }
      /deep/ .el-select:hover .el-input__inner,
      /deep/ .el-input__inner {
        background: #ffffff;
      }
    }
    &::before {
      content: "";
      position: absolute;
      top: 0;
      width: 100%;
      left: 0;
      border: 1px Dashed #e1e9ef;
    }
    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      width: 100%;
      left: 0;
      border: 1px Dashed #e1e9ef;
    }
  }
  
  /*在谷歌下移除input[number]的上下箭头*/
  .payment_box /deep/ input[type="number"]::-webkit-outer-spin-button,
  .payment_box /deep/ input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    margin: 0;
  }
  
  /*在firefox下移除input[number]的上下箭头*/
  .payment_box /deep/ input[type="number"] {
    -moz-appearance: textfield;
  }