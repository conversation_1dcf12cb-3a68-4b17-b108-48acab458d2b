<template>
  <div class="Schedule_box">
    <div class="shift_box">
      <p class="shift_title">Set Shift Times</p>
      <div class="shift_time">
        <table class="shift_table" cellspacing="0">
          <tr class="shift_th">
            <th style="width:300px;"></th>
            <th>Shift1</th>
            <th>Shift2</th>
            <th>Shift3</th>
          </tr>
          <tr class="shift_tr">
            <td class="shift_tip">Start of Shift</td>
            <td v-for="index in 3" :key="index">
              <el-time-picker :disabled="disabled" v-model="shiftTimes[index-1].start_time" value-format="HH:mm:ss" format="hh:mm A" placeholder="Arbitrary time"></el-time-picker>
            </td>
          </tr>
          <tr class="shift_tr">
            <td class="shift_tip">End of Shift</td>
            <td v-for="index in 3" :key="index">
              <el-time-picker :disabled="disabled" v-model="shiftTimes[index-1].end_time" value-format="HH:mm:ss" format="hh:mm A" placeholder="Arbitrary time"></el-time-picker>
            </td>
          </tr>
        </table>
      </div>
       <div class="shift_btn">
          <el-button @click="saveShiftTime" :disabled="disabled" :loading="timeLoading">Confirm</el-button>
        </div>
    </div>
    <div style="margin:20px  0;padding-top:20px; " class="shift_box">
      <div class="shift_time">
        <table class="shift_table" cellspacing="0">
          <tr class="shift_th">
            <th style="width:300px;text-align: left">After Hours Lead Rotation</th>
            <th style="text-align: left">PM</th>
          </tr>
          <tr class="shift_tr">
            <td class="shift_tip">Agents in After Hours Rotation</td>
            <td style="text-align: left">
              <el-select v-model="orderUsersID" multiple :disabled="disabled">
                <el-option v-for="(item,index) in users" :key="index" :label="`${item.first_name} ${item.last_name}`" :value="item.user_id"></el-option>
              </el-select>
            </td>
          </tr>
          <tr class="shift_tr">
            <td colspan="2">
              <div class="shift_btn">
                <el-button @click="saveOrder" :disabled="disabled" :loading="rotationLoading">Confirm</el-button>
              </div>
            </td>
          </tr>
        </table>
      </div>
    </div>
    <div class="Schedule_table shift_box">
      <p class="shift_title">Agent Schedule</p>
      <!-- <el-divider></el-divider> -->
      <div>
        <el-table :data="schedules" style="width: 100%" :row-class-name="tableRowClassName">
          <el-table-column label="Day" prop="date" width="240px"></el-table-column>
          <el-table-column label="Shift1" min-width="150">
            <template slot-scope="scope">
              <div v-for="(item,index) in scope.row.shift1Users" :key="index">
                <p>{{item.user_name}}</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="Shift2" min-width="150">
            <template slot-scope="scope">
              <div v-for="(item,index) in scope.row.shift2Users" :key="index">
                <p>{{item.user_name}}</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="Shift3" min-width="150">
            <template slot-scope="scope">
              <div v-for="(item,index) in scope.row.shift3Users" :key="index">
                <p>{{item.user_name}}</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column width="100">
            <template slot-scope="scope">
              <el-button @click="editSchedule(scope.row)" style="color:red;" type="text" size="small" :disabled="disabled">Edit</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="form_btn">
        <el-button @click="preMonth">Last Month</el-button>
        <el-button @click="nextMonth">Next Month</el-button>
      </div>
    </div>
    <!-- 修改排班弹出框 -->
    <el-dialog title="Shift" :visible.sync="showDialog" >
      <div class="shift_puoup">
        <div class="puoup_row">
          <span>Shift 1:</span>
          <el-select v-model="selectedSchedule.shift1Users" multiple placeholder="select">
            <el-option v-for="item in users" :key="item.user_id" :label="`${item.first_name} ${item.last_name}`" :value="item.user_id"></el-option>
          </el-select>
        </div>
        <div class="puoup_row">
          <span>Shift 2:</span>
          <el-select v-model="selectedSchedule.shift2Users" multiple placeholder="select">
            <el-option v-for="item in users" :key="item.user_id" :label="`${item.first_name} ${item.last_name}`" :value="item.user_id"></el-option>
          </el-select>
        </div>
        <div class="puoup_row">
          <span>Shift 3:</span>
          <el-select v-model="selectedSchedule.shift3Users" multiple placeholder="select">
            <el-option v-for="item in users" :key="item.user_id" :label="`${item.first_name} ${item.last_name}`" :value="item.user_id"></el-option>
          </el-select>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="saveSchedule">Confirm</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import dayjs from "dayjs";
import { apiGetShiftTime, apiCreateShiftTime, apiGetShiftOrder, apiCreateShiftOrder, apiGetSchedule, apiCreateSchedule, apiGetUsers,} from "../../API/api";
const SHIFT1 = "Shift 1";
const SHIFT2 = "Shift 2";
const SHIFT3 = "Shift 3";
export default {
  data() {
    return {
      showDialog: false,
      current: 0, // 用来记录上一个月下一个月，上一个月减1，下一个月加1
      shiftTimes: [
        { name: SHIFT1, start_time: null, end_time: null, },
        { name: SHIFT2, start_time: null, end_time: null, },
        { name: SHIFT3, start_time: null, end_time: null, },
      ],
      shiftMap: {}, // 存放shift名称对应的ID
      orderUsers: [], // 轮班的用户
      orderUsersID: [], // 只存放轮班的用户的id
      users: [], // pm 和 admin 列表
      schedules: [], // 轮班列表
      selectedSchedule: {}, // 存放编辑排班选择的行数据
      timeLoading: false,
      rotationLoading: false,
    };
  },
  computed: {
    year() {
      return dayjs().add(this.current, "month").year();
    },
    month() {
      return dayjs().add(this.current, "month").month() + 1; // 月份以0开始，所以要加1
    },
    count() {
      // 当月的天数
      return dayjs().add(this.current, "month").daysInMonth();
    },
    disabled() {
      return this.$store.state.userInfo.is_admin === false;
    },
  },
  methods: {
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex % 2 != 0) {
        return "become_angry";
      }
      return "";
    },
    // 上一个月
    preMonth() {
      this.current--;
      this.initSchedule();
      this.getSchedule();
    },
    // 下一个月
    nextMonth() {
      this.current++;
      this.initSchedule();
      this.getSchedule();
    },
    // 初始化当月的轮班数据
    initSchedule() {
      this.schedules = [];
      for (let i = 0; i < this.count; i++) {
        let date = dayjs(new Date(this.year, this.month - 1, 1))
          .add(i, "day")
          .format("dddd MMMM DD YYYY");
        this.schedules.push({
          date: date,
          shift1Users: [],
          shift2Users: [],
          shift3Users: [],
        });
      }
    },
    // 编辑轮班
    editSchedule(val) {
      this.showDialog = true;
      this.selectedSchedule = {
        date: dayjs(val.date).format("YYYY-MM-DD"),
        shift1Users: val.shift1Users.map((user) => user.user_id),
        shift2Users: val.shift2Users.map((user) => user.user_id),
        shift3Users: val.shift3Users.map((user) => user.user_id),
      };
    },
    // 设置轮班时间
    saveShiftTime() {
      this.timeLoading = true;
      apiCreateShiftTime({ shift_times: this.shiftTimes })
        .then((res) => {
          this.$message.success("Update shift time successful");
          res.shift_times.forEach((shiftTime) => {
            if (shiftTime.name === SHIFT1) {
              this.$set(this.shiftTimes, 0, shiftTime);
              this.shiftMap[SHIFT1] = shiftTime.shift_time_id;
            } else if (shiftTime.name === SHIFT2) {
              this.$set(this.shiftTimes, 1, shiftTime);
              this.shiftMap[SHIFT2] = shiftTime.shift_time_id;
            } else if (shiftTime.name === SHIFT3) {
              this.$set(this.shiftTimes, 2, shiftTime);
              this.shiftMap[SHIFT3] = shiftTime.shift_time_id;
            }
          });
        })
        .catch((err) => {
          this.$message.error("Update shift time fail");
          this.$alert(err.response.data.detail, { confirmButtonText: "OK" });
        })
        .finally(() => { 
          this.timeLoading=false
        });
    },
    // 设置轮班顺序
    saveOrder() {
      this.rotationLoading = true;
      apiCreateShiftOrder({ users: this.orderUsersID })
        .then((res) => {
          this.$message.success("Update rotation successful");
          this.orderUsers = res.shift_orders;
          this.orderUsersID = res.shift_orders.map((user) => user.user_id);
        })
        .catch((err) => {
          this.$message.error("Update rotation fail");
          this.$alert(err.response.data.detail, { confirmButtonText: "OK" });
        })
        .finally(() => {
          this.rotationLoading = false;
        });
    },
    // 设置轮班
    saveSchedule() {
      let schedules = [];
      schedules.push({
        date: this.selectedSchedule.date,
        shift_time: this.shiftMap[SHIFT1],
        users: this.selectedSchedule.shift1Users,
      });
      schedules.push({
        date: this.selectedSchedule.date,
        shift_time: this.shiftMap[SHIFT2],
        users: this.selectedSchedule.shift2Users,
      });
      schedules.push({
        date: this.selectedSchedule.date,
        shift_time: this.shiftMap[SHIFT3],
        users: this.selectedSchedule.shift3Users,
      });
      apiCreateSchedule({ schedules: schedules })
        .then((res) => {
          this.showDialog = false;
          this.$message.success("Update schedule successful");
          this.schedules = res.schedules.reduce((previous, current) => {
            let date = dayjs(current.date).date();
            if (current.shift_time.name === SHIFT1) {
              previous[date - 1]["shift1Users"] = current.users;
            } else if (current.shift_time.name === SHIFT2) {
              previous[date - 1]["shift2Users"] = current.users;
            } else if (current.shift_time.name === SHIFT3) {
              previous[date - 1]["shift3Users"] = current.users;
            }
            return previous;
          }, this.schedules);
        })
        .catch((err) => {
          this.$message.error("Update schedule fail");
        })
        .finally(() => {});
    },
    getSchedule() {
      // 获取轮班列表
      apiGetSchedule({ year: this.year, month: this.month, limit: 93 }).then(
        (res) => {
          this.schedules = res.results.reduce((previous, current) => {
            let date = dayjs(current.date).date();
            if (current.shift_time.name === SHIFT1) {
              previous[date - 1]["shift1Users"] = current.users;
            } else if (current.shift_time.name === SHIFT2) {
              previous[date - 1]["shift2Users"] = current.users;
            } else if (current.shift_time.name === SHIFT3) {
              previous[date - 1]["shift3Users"] = current.users;
            }
            return previous;
          }, this.schedules);
        }
      );
    },
  },
  async created() {
    this.initSchedule();

    // 获取pm和admin
    apiGetUsers({ user_type: ["pm"] }).then((res) => {
      this.users = res.results;
    });

    // 获取轮班顺序
    apiGetShiftOrder().then((res) => {
      this.orderUsers = res.results;
      this.orderUsersID = res.results.map((user) => user.user_id);
    });

    // 获取轮班时间
    await apiGetShiftTime().then((res) => {
      res.results.forEach((shiftTime) => {
        if (shiftTime.name === SHIFT1) {
          this.$set(this.shiftTimes, 0, shiftTime);
          this.shiftMap[SHIFT1] = shiftTime.shift_time_id;
        } else if (shiftTime.name === SHIFT2) {
          this.$set(this.shiftTimes, 1, shiftTime);
          this.shiftMap[SHIFT2] = shiftTime.shift_time_id;
        } else if (shiftTime.name === SHIFT3) {
          this.$set(this.shiftTimes, 2, shiftTime);
          this.shiftMap[SHIFT3] = shiftTime.shift_time_id;
        }
      });
    });

    this.getSchedule();
  },
};
</script>
<style lang="scss" scoped>
/*弹出框  */
.puoup_row {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 20px;
  span {
    display: inline-block;
    margin-right: 10px;
  }
  /deep/ .el-select {
    width: 70%;
  }
}

.Schedule_box {
  /deep/ .el-dialog__header {
    background-color: #668994;
    text-align: left;
  }
  /deep/ .el-dialog__title {
    color: #fff;
  }
  /deep/ .el-input__inner {
    padding: 10px;
    width: 100%;
    line-height: normal;
  }
  /deep/ .el-input__inner {
    padding-left: 30px;
  }
  /deep/ .el-dialog__footer {
    text-align: center;
  }
}

.shift_box .Schedule_box /deep/ .el-dialog__title,
.Schedule_box /deep/ .el-dialog__headerbtn .el-dialog__close {
  color: #fff;
}

.dialog-footer /deep/ .el-button {
  background-color: #668994;
  color: #fff;
  padding: 15px 60px;
}

/*  */
.Schedule_table {
  /deep/ .el-table {
    .become_angry {
      background-color: #e1e7e9;
    }
    .el-table__body tr:hover > td,
    .el-table__body tr.current-row > td {
      cursor: pointer;
    }
    th {
      background-color: #eff3f5;
      border: none;
    }
  }
}
.form_btn {
  margin-top: 40px;
  text-align: right;
  /deep/ .el-button {
    background-color: #668994;
    padding: 15px 60px;
    color: #fff;
    font-size: 18px;
  }
}

/*  */
.shift_box {
  background-color: #fff;
  padding: 10px 30px 40px;
  border-radius: 6px;
}

/*  */
.shift_btn {
  text-align: right;
  margin-top: 20px;
  /deep/ .el-button {
    background-color: #668994;
    padding: 15px 60px;
    color: #fff;
    font-size: 18px;
    font-family: PingFangSC-Semibold, sans-serif;
  }
  /deep/ .el-button.is-disabled,
  /deep/ .el-button.is-disabled:focus,
  /deep/ .el-button.is-disabled:hover {
    color: #c0c4cc;
    cursor: not-allowed;
    background-image: none;
    background-color: #f5f7fa;
    border: 0.017857rem solid #dcdfe6;
  }
}

/*  */
.shift_time {
  .shift_table {
    width: 100%;
    min-width: 860px;
  }
  @media (max-width: 940px) {
    overflow-x: scroll;
  }
}

.shift_th {
  height: 40px;
  background: #eff3f5 0% 0% no-repeat padding-box;
  border-radius: 8px;
  th {
    font-size: 20px;
    font-weight: normal;
    padding-left: 10px;
  }
}
.shift_tip {
  margin-left: 40px;
  text-align: left;
  font-size: 16px;
}
.shift_tr td {
  padding: 10px 10px;
}
.shift_tr /deep/ .el-select {
  width: 100%;
}
.shift_title {
  text-align: left;
  font-size: 20px;
  margin: 0px 0 10px 0;
  color: #333333;
  font-family: PingFangSC-Medium, sans-serif;
}
/deep/ .el-dialog{width: 400px;}
@media (max-width:992px) {
  /deep/ .el-dialog{width: 90%;}
}
</style>
