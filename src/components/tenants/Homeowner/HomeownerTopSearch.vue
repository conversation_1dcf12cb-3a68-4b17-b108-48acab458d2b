<template>
  <div class="search">
    <!-- <div class="tenant_top">
      <p class="big_btn">
        <el-button icon="el-icon-plus" @click="addHomeowner">add New Homeowners</el-button>
      </p>
    </div> -->
    <!-- <el-divider></el-divider> -->
    <div class="tenant_filter">
      <div class="tenant_form">
        <el-form style="width:100%" :model="data" ref="data">
          <el-row :gutter="10" style="width:100%">
            <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
              <div class="grid-content bg-purple">
                <div>
                  <el-form-item label="First Name">
                    <el-input v-model="data.first_name"></el-input>
                  </el-form-item>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
              <div class="grid-content bg-purple-light">
                <div>
                  <el-form-item label="Last Name">
                    <el-input v-model="data.last_name"></el-input>
                  </el-form-item>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
              <div class="grid-content bg-purple">
                <div>
                  <el-form-item label="Company Name">
                    <el-input v-model="data.company_name"></el-input>
                  </el-form-item>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
              <div class="grid-content bg-purple">
                <div>
                  <el-form-item label="Email" prop="email"
                    :rules="[ { type: 'email', message: 'Please input correct email address', trigger:'blur'}]">
                    <el-input v-model="data.email"></el-input>
                  </el-form-item>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
              <div class="grid-content bg-purple-light">
                <div>
                  <el-form-item label="Phone">
                    <el-input
                      v-model="data.phone"
                      type="tel"
                      maxlength="22"
                      @input="e=>data.phone=changePhone(e)"
                    ></el-input>
                  </el-form-item>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
              <div class="grid-content bg-purple-light">
                <div>
                  <el-form-item label="Rental Address">
                    <el-input v-model="data.property_address"></el-input>
                  </el-form-item>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="tenant_btn">
        <el-button class="tenant_search" icon="el-icon-plus" @click="addHomeowner">Add New Homeowners</el-button>
        <el-button class="tenant_search" @click="search('data')">Search</el-button>
        <el-button class="tenant_reset" @click="clear">Reset Filters</el-button>
      </div>
    </div>
  </div>
</template>
<script>
import { Minixs } from "../../../js/mixins";
export default {
  mixins: [Minixs],
  data() {
    return {
      data: {}
    };
  },
  methods: {
    // 创建新HO
    addHomeowner() {
      this.$router.push({ name: "create-homeowner" });
    },
    search(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.$emit("search", this.data);
        } else {
          return false;
        }
      });
    },
    // 清空过滤
    clear() {
      this.data.first_name = this.data.last_name = this.data.property_address = this.data.email = this.data.phone = this.data.company_name =
        "";
    }
  }
};
</script>
<style lang="scss" scoped>
.tenant_filter {
  background: #fff;
  padding: 0px 40px 20px;
  border-radius: 10px;
  margin-bottom: 20px;
  /deep/ .el-input__inner {
    height: 36px;
    line-height: normal;
    width: 98%;
    margin: 1px 1px 1px 0;
  }
  .tenant_form {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    /deep/ .el-col {
      height: 80px;
    }
    /deep/ .el-form-item {
      margin-bottom: 10px;
    }
    /deep/ .el-input__inner {
      height: 36px;
      line-height: normal;
      width: 99%;
      margin: 1px 1px 1px 0;
    }
    /deep/ .el-select,
    /deep/ .el-form {
      width: 100%;
    }
  }
  .tenant_btn {
    text-align: right;
    padding-right: 13px;
    button {
      height: 40px;
      padding: 9px 35px;
      background: #ffffff 0% 0% no-repeat padding-box;
      border: none;
      border-radius: 4px;
    }
    .tenant_search {
      background-color: #678993;
      color: #fff;
      margin-right: 10px;
    }
    .tenant_reset {
      border: 1px solid #e5e5e5;
    }
  }
}
.search{
  background-color: #fff;
  /deep/ .el-divider--horizontal{
    margin: 0;
  }
}
// .tenant_top {
//   background: #fff;
//   padding: 20px 40px;
//   height: 80px;
//   border-radius: 10px;
//   display: flex;
//   justify-content: space-between;
//   align-items: center;
//   padding: 0 40px;
//   .el-button {
//     height: 40px;
//     &:focus,
//     &:hover {
//       color: #606266;
//       border-color: #dcdfe6;
//       background-color: #fff;
//     }
//   }
// }
</style>