// 保存的按钮
.propety_save {
  z-index: 1;
  right: 10%;
  width: 50px;
  height: 50px;
  bottom: 140px;
  position: fixed;
}

.propety {
  background: #fff;
  padding-bottom: 100px;

  .pages,
  .sg_cate,
  .text-reason {
    padding: 0 20px 0 40px;
  }

  /deep/ .el-form-item__content {
    line-height: 15px;
  }

  /deep/ .el-form-item {
    margin-bottom: 15px;
  }

  /deep/ .el-input__inner {
    height: 36px;
    line-height: normal;
  }

  .form_in {
    text-align: left;
    font-size: 16px;

    /deep/ .el-select,
    /deep/ .el-autocomplete {
      width: 100%;
    }

    /deep/ .el-select,
    /deep/ .el-autocomplete {
      width: 100%;
    }

    /deep/ .el-textarea__inner {
      font-family: "Roboto-Regular", sans-serif;
    }

    /deep/ .grid-content {
      margin-top: 10px;
    }

    .top-prodect {
      padding: 20px 0 10px 0;

      .title-text {
        margin: 0 5px 0 0;
        font-family: "Roboto-Bold", sans-serif;
        font-size: 18px;
      }

      i {
        font-size: 18px;
        font-weight: 600;
        color: #678993;
      }

      &:hover {
        cursor: pointer;
      }
    }

    .propety-title {
      font-size: 14px;
      line-height: 24px;
      margin: 0;
      font-family: "Roboto-Regular", sans-serif;
      color: #38425b;
    }

    .show-margin {
      margin-top: 10px;
    }

    .set_width {
      width: 100%;

      .propety_row {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        -webkit-flex-wrap: wrap;
        flex-wrap: wrap;
        width: 100%;

        .propety_type {
          width: calc(calc(100% - 20px) / 2);

          &:first-child {
            margin-right: 20px;
          }
        }
      }

      /deep/ .el-input input::-webkit-outer-spin-button,
      /deep/ .el-input input::-webkit-inner-spin-button {
        -webkit-appearance: none;
      }

      /deep/ .el-input input[type="number"] {
        -moz-appearance: textfield;
      }
    }

    .check-row {
      align-items: flex-start;
      -webkit-flex-wrap: wrap;
      flex-wrap: wrap;

      .checkbox-item {
        width: calc(calc(100% - 40px) / 5);
        margin-right: 10px;
        margin-bottom: 15px;

        // width: 200px;
        &:nth-of-type(5n) {
          margin-right: 0;
        }

        /deep/ .el-checkbox-button {
          width: 100%;
          border-radius: 4px;
          font-family: "Roboto-Regular", sans-serif;

          .el-checkbox-button__inner {
            width: 100%;
            background: #f4f9fc;
            padding-left: 0;
            padding-right: 0;

            &:hover {
              background: #dcdfe6;
              color: #606266;
              border-color: #dcdfe6;
            }

            @media (max-width: 1400px) {
              font-size: 12px;
            }
          }
        }

        /deep/ .el-checkbox-button.is-checked:first-child .el-checkbox-button__inner {
          background: #678993;
          color: #fff;
          border-color: #678993;
        }

        /deep/ .el-checkbox-button.is-focus .el-checkbox-button__inner {
          border-color: #f2f2f2;
        }

        /deep/ .el-checkbox-button:last-child .el-checkbox-button__inner {
          width: 100%;
        }
      }
    }
  }

  @media (max-width:992px) {
    .form_in {
      .set_width .propety_row {
        display: block;

        .propety_type {
          width: 100%;
        }
      }

      .check-row {
        margin-top: 10px;

        .checkbox-item {
          min-width: 200px;

          &:nth-of-type(5n) {
            margin-right: 10px;
          }
        }
      }
    }
  }
}

.sg_cate {
  margin-top: 0px;
  text-align: justify;
  display: -webkit-flex;
  -webkit-flex-wrap: wrap;
  flex-wrap: wrap;

  /deep/ .el-radio {
    margin-top: 10px;
  }

  /deep/ .el-radio__label {
    font-size: 17px;
  }

  /deep/ .el-radio__input .el-radio__inner {
    width: 16px;
    height: 16px;
    border-radius: 0;

    &::after {
      width: 0;
    }
  }

  /deep/ .el-radio__input.is-checked .el-radio__inner {
    background-color: #fff;
    background-image: url("../../assets/icon/ico-checkbox.svg");
    background-size: cover;
    border: none;
  }

  /deep/ .el-radio__input.is-checked+.el-radio__label {
    color: #666;
  }
}

// 原因
.text-reason {
  width: 800px;

  p {
    color: #666;
    font-family: "Roboto-Regular", sans-serif;
    text-align: left;
    font-size: 16px;
  }
}

/* 上传文件 */
.upload_file {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin: 0 0 10px 0;
  height: 30px;

  /deep/ .el-button--primary {
    border-color: #fff;
    background: none;
    padding: 0;
  }
}

/* 关联卧室 */
.relation {
  margin-bottom: 15px;

  /deep/ .el-switch__label.is-active {
    color: #303133;
  }
}

/*卧室床的类型  */
.room-small-title {
  font-size: 16px;
  margin: 10px 0 0 0;
  font-weight: normal;
  font-family: "Roboto-Bold", sans-serif;
}

.propety_icon {
  .remove-btn {
    display: inline-block;
    height: 75px;
    text-align: left;
    width: 30px;
    margin: 0;
    line-height: 6;
  }
  /deep/ .el-form-item.bed_number {
    width: calc(100% - 40px);
  }
}

.increase_btn {
  margin: 0px 0 20px 0;

  .btn-add {
    text-decoration: underline;
    color: #72949d;
    font-family: "Roboto-Bold", sans-serif;
    font-size: 14px;

    &:hover {
      cursor: pointer;
    }
  }
}

.btn-save {
  text-align: right;
  margin-top: 30px;
  position: relative;
  padding-top: 50px;

  .el-button {
    padding: 10px;

    .btn-row {
      width: 136px;
      margin: 0;
      justify-content: space-between;

      i {
        font-family: "Roboto-Bold", sans-serif;
        font-size: 20px;
      }
    }
  }

  &::before {
    content: "";
    position: absolute;
    top: 10px;
    left: 0;
    width: 100%;
    border: 1px solid #e1e9ef;
  }
}
@media (max-width:1200px) {
  .propety_icon {
    .bed_row {
      display: flex !important;
    }
    .property_bed {
      width: 100% !important;

    }
  }
}

@media (max-width:992px) {
  .propety_icon .bedroom_row .propety_level {
    width: 100%;
  }
}