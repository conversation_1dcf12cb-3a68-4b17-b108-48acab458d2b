.calendar-right-top {
    justify-content: space-between;
    .calendar-right-title {
      text-align: left;
      font-size: 18px;
      margin: 0;
      font-family: "Roboto-Bold", sans-serif;
    }
  }
  .calendar-note {
    .note_btn {
      text-align: left;
      margin-top: 10px;
      a {
        color: #678993;
        font-family: "Roboto-Bold", sans-serif;
        text-decoration: underline;
        &:hover {
          cursor: pointer;
        }
      }
    }
  }
  .update-row {
    justify-content: space-between;
    flex-direction: row-reverse;
    margin-top: 10px;
    .update-btn{
      margin-left: 20px;
    }
  }
  .last-update {
    text-align: left;
    font-family: "Roboto-Bold", sans-serif;
    font-size: 16px;
  }
  // date
  .calendar-date {
    margin-top: 37px;
    position: relative;
    .prev-month,
    .next-month {
      position: absolute;
      top: 10px;
      color: #72809d;
      font-size: 20px;
      i {
        font-weight: 600;
      }
    }
    .next-month {
      right: 0;
    }
  }
  // 颜色
  .explain-box {
    text-align: center;
    .explain {
      justify-content: center;
      flex-wrap: wrap;
      width: 474px;
      height: 70px;
      // background: #ffffff 0% 0% no-repeat padding-box;
      // box-shadow: 0px 3px 6px #00000029;
      // border-radius: 4px;
      .explain_setting {
        justify-content: flex-start;
        // width: 110px;
        .setting_color {
          width: 10px;
          height: 10px;
          display: inline-block;
          // border: 2px solid #707070;
          border-radius: 50%;
          margin: 0 10px 0 20px;
        }
        .setting_color_other {
          background-color: rgba(222, 27, 75, 0.5);
        }
        .setting_color_owner {
          background-color: rgba(103, 137, 147, 0.5);
        }
        .setting_color_lease {
          background-color: rgba(212, 175, 55, 0.5);
        }
      }
      &:hover {
        cursor: pointer;
      }
    }
    @media (max-width:772px) {
      .explain{display: block;}
    }
  }
  .explain_row {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .select-type {
    /deep/ .el-dialog {
      .el-dialog__header {
        height: 0;
        padding: 0;
      }
      .el-dialog__body {
        padding-top: 10px;
      }
      .select-title {
        padding-right: 40px;
        line-height: 0.428571rem;
        font-size: 0.321429rem;
        color: #303133;
      }
    }
  }
  
  // 选择类型
  .select_type {
    /deep/ .el-radio__input.is-checked .el-radio__inner {
      border: none;
      background-image: url("../../assets/img/true.png");
      background-size: cover;
      width: 20px;
      height: 20px;
    }
    /deep/ .el-radio__input.is-checked + .el-radio__label {
      color: #333;
    }
    /deep/ .el-radio__inner {
      width: 20px;
      height: 20px;
      &:hover {
        border-color: #678993;
      }
      &::after {
        display: none;
      }
    }
  }
  
  .dialog-footer {
    .el-button {
      &:last-child {
        background-color: #678993;
        color: #fff;
        &:hover {
          background-color: #57737b;
        }
      }
    }
  }
  
  //
  .calendarsimple {
    font-size: 16px;
    .calendar-contect {
      position: relative;
      padding: 20px;
      &::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        border: 1px solid #e1e9ef;
      }
    }
    .pb_btn {
      margin-top: 30px;
      display: flex;
      justify-content: flex-end;
      height: 80px;
      align-items: center;
      padding: 0 20px;
      .next-btn {
        padding: 8px 20px 8px 20px;
        .btn-row {
          width: 100px;
          margin: 0;
          justify-content: space-between;
          i {
            font-family: "Roboto-Bold", sans-serif;
            font-size: 20px;
          }
        }
      }
    }
  }
  
  .canclder {
    font-size: 16px;
    margin-top: 30px;
    .date {
      display: flex;
      width: 100%;
      justify-content: space-between;
      margin: 0 auto;
      align-items: center;
      height: 50px;
      .creat_box {
        display: flex;
        height: 50px;
        align-items: baseline;
        justify-content: flex-end;
      }
      .wrapper {
        width: 100%;
        margin: 0 auto;
      }
    }
  }
  
  .box {
    width: 100px;
    font-size: 16px;
    cursor: pointer;
    color: #999;
    margin-bottom: 20px;
  }
  
  .prev-month {
    text-align: left;
  }
  
  .next-year {
    text-align: right;
  }
  
  .same_time {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    height: 350px;
    .calendar {
      width: calc(calc(100% - 60px) / 2);
    }
    /deep/ .el-input {
      input[type="number"] {
        -moz-appearance: textfield;
      }
      input::-webkit-outer-spin-button,
      input::-webkit-inner-spin-button {
        -webkit-appearance: none;
      }
    }
  }
  /deep/ .el-dialog{
    width: 450px;
  }
  @media screen and (max-width: 1000px) {
    .same_time .calendar {
      width: calc(calc(100% - 40px) / 2);
    }
  }
  @media screen and (max-width: 992px) {
    .update-row{display: block;text-align: left;.update-btn{margin-left: 0;}.block-btn{margin:0 20px 20px 0;}}
    .same_time {
      height: auto;
      .calendar {
      width: calc(calc(100% - 20px) / 2);
    }}
    /deep/ .el-dialog{
      width: 90%;
    }
  }
  @media screen and (max-width: 772px) {
    .wrapper .same_time {
      display: block;
      .calendar {
        width: 100%;
      }
    }
  }