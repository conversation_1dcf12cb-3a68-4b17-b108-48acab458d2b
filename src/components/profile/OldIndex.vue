<template>
  <div class="profile">
    <el-form :model="data" ref="data">
      <el-row :gutter="10">
        <el-col :xs="24" :sm="12" :md="8" :lg="6" style="padding-right:20px">
          <div class="grid-content">
            <el-form-item
              prop="first_name"
              :rules="[{ required: true, message: 'this information is required', trigger: 'blur' }]"
            >
              <p class="profile_tip">FIRST NAME</p>
              <el-input v-model="data.first_name" />
            </el-form-item>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6" style="padding-right:20px">
          <div class="grid-content">
            <el-form-item
              prop="last_name"
              :rules="[{ required: true, message: 'this information is required', trigger: 'blur' }]"
            >
              <p class="profile_tip">LAST NAME</p>
              <el-input v-model="data.last_name" />
            </el-form-item>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6" style="padding-right:20px">
          <div class="grid-content">
            <el-form-item>
              <p class="profile_tip">TITLE</p>
              <el-input v-model="data.title" />
            </el-form-item>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6" style="padding-right:20px">
          <div class="grid-content">
            <el-form-item
              prop="email"
              :rules="[{ required: true, message: 'this information is required', trigger: 'blur' },{ type: 'email', message: 'Please input correct email address', trigger: ['blur', 'change'] }]"
            >
              <p class="profile_tip">EMAIL</p>
              <el-input v-model="data.email" />
            </el-form-item>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6" style="padding-right:20px">
          <div class="grid-content">
            <el-form-item>
              <p class="profile_tip">CELL PHONE</p>
              <el-input
                v-model="data.phone"
                type="tel"
                maxlength="22"
                @input="e=>data.phone=changePhone(e)"
              />
            </el-form-item>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6" style="padding-right:20px">
          <div class="grid-content">
            <el-form-item>
              <p class="profile_tip">AGENT EXTENSION</p>
              <el-input
                v-model="data.phone_extension"
                maxlength="22"
                @input="e=>data.phone_extension=changePhone(e)"
              />
            </el-form-item>
          </div>
        </el-col>

        <el-col :xs="24" :sm="12" :md="8" :lg="6" style="padding-right:20px">
          <div class="grid-content">
            <el-form-item>
              <p class="profile_tip">EDUCATION</p>
              <el-input v-model="data.education" />
            </el-form-item>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6" style="padding-right:20px">
          <div class="grid-content">
            <el-form-item>
              <p class="profile_tip">COMMUNITY ORGANIZATIONS</p>
              <el-input v-model="data.community_organization" />
            </el-form-item>
          </div>
        </el-col>
        <el-col :xs="24" :sm="24" :md="24" :lg="24" style="padding-right:20px">
          <div class="grid-content">
            <p class="profile_tip profile_sccial">SOCIAL MEDIA</p>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6" style="padding-right:20px">
          <div class="grid-content">
            <div class="profile_social_row">
              <span class="profile_icon">
                <img src="../../assets/img/smaill_link.png" alt />
              </span>
              <el-form-item>
                <el-input v-model="data.social.link" />
              </el-form-item>
            </div>
          </div>
        </el-col>

        <el-col :xs="24" :sm="12" :md="8" :lg="6" style="padding-right:20px">
          <div class="grid-content">
            <div class="profile_social_row">
              <span class="profile_icon">
                <img src="../../assets/img/smaill_instag.png" alt />
              </span>
              <el-form-item>
                <el-input v-model="data.social.instagram" />
              </el-form-item>
            </div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6" style="padding-right:20px">
          <div class="grid-content">
            <div class="profile_social_row">
              <span class="profile_icon">
                <img src="../../assets/img/smaill_face.png" alt />
              </span>
              <el-form-item>
                <el-input v-model="data.social.facebook" />
              </el-form-item>
            </div>
          </div>
        </el-col>
      </el-row>
      <div class="profile_bio">
        <p class="tip">BIO</p>
        <el-input type="textarea" resize="none" v-model="data.bio" :autosize="{minRow:10 }"></el-input>
      </div>
      <div class="profile_upload">
        <p class="tip">HEADSHON</p>
        <el-upload
          class="avatar-uploader"
          action="uploader"
          :show-file-list="false"
          :http-request="upProfileImg"
        >
          <img v-if="data.avatar" :src="data.avatar" class="avatar" />
          <img v-else src="../../assets/img/uploadImg.jpg" class="avatar" alt />
        </el-upload>
      </div>
      <div class="profile_btn">
        <el-button @click="saveProfileChange">SAVE CHANGES</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
import {
  apiUploadImage,
  apiUpdateProfile,
  apiGetProfile
} from "../../API/api";
import { Minixs } from "../../js/mixins";
export default {
  mixins: [Minixs],
  data() {
    return {
      data: { avatar: "", social: { facebook: "", instagram: "", link: "" } },
      rules: {}
    };
  },
  methods: {
    upProfileImg(file) {
      let files = new FormData();
      files.append("file", file.file);
      apiUploadImage(files).then(res => {
        this.data.avatar = res.url;
      });
    },
    saveProfileChange() {
      this.$refs.data.validate(valid => {
        if (valid) {
          apiUpdateProfile(this.data)
            .then(res => {
              this.$store.commit("setUserInfo", res);
              this.$message.success("Modified success");
            })
            .catch(err => {
              this.$message.error("Please resubmit");
            });
        } else {
          return false;
        }
      });
    }
  },
  created() {
    apiGetProfile().then(res => {
      this.data = res;
    });
  }
};
</script>
<style lang="scss" scoped>
.profile {
  background-color: #fff;
  padding: 20px;
  font-size: 16px;
  text-align: left;
  .tip {
    margin-bottom: 10px;
    font-size: 13px;
  }
  .profile_tip {
    margin: 0px;
    font-size: 13px;
  }
  /deep/ .el-form-item{
    margin-bottom: 10px;
  }
  .profile_sccial {
    margin-bottom: 10px;
  }
  .profile_social_row {
    display: flex;
    align-items: center;
    .profile_icon {
      margin-right: 10px;
      img {
        height: 40px;
        width: 40px;
      }
    }
    /deep/  .el-form-item {
      margin-bottom: 0;
      width: 100%;
    }
  }
  .profile_btn {
    text-align: center;
    .el-button {
      margin-top: 10px;
      background-color: #678993;
      color: #fff;
      padding: 15px 60px;
    }
  }
  .profile_upload {
    .avatar {
      width: 200px;
      height: 178px;
      display: block;
      border-radius: 4px;
    }
  }
  @media (max-width: 772px) {
    .tip,.profile_tip{
      font-size: 14px;
    }
  }
}
</style>
