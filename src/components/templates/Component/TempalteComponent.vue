<template>
  <div>
    <div class="form">
      <div class="upload">
        <p class="form_title"><slot name="topName"></slot></p>
        <el-upload class="upload-demo" action="upload-demo" :http-request="fileChange" :show-file-list="false">
          <slot name="btn"></slot>
        </el-upload>
      </div>
      <el-divider></el-divider>
      <div class="tag">
        <el-tag v-for="tag in data" :key="tag.template_file_uuid" closable  @close="closeTag(tag)" @click="clickView(tag)">{{tag.file_name}}</el-tag>
      </div>
    </div>
  </div>
</template>
<script>
import { apiUploadTempalte ,apiCreateTempalteFile} from "../../../API/api";
export default {
  props: ["data","category","self"],
  methods: {
    fileChange(param) {
      var fileObj = param.file;
      var form = new FormData();
      form.append("file", fileObj);
      apiUploadTempalte(form).then(res => {
          let data = {
            file: res.object_uuid,
            category: this.category,
            self: this.self
          };
          apiCreateTempalteFile(data).then(res => {
              this.$emit("uploadFile", res);
            }).catch(err => {
              this.$message.error("Upload failed, please upload again");
            });
        }).catch(err => {
          this.$message.error("Upload failed, please upload again");
        });
    },
    closeTag(tag) {
        this.$emit("closeTag",tag)
    },
    clickView(tag) {
      window.open(tag.url, "_blank");
    }
  }
};
</script>
<style lang="scss" scoped>
.form {
  background-color: #fff;
  border-radius: 10px;
  margin-bottom: 30px;
  padding-bottom: 20px;
  /deep/ .el-divider--horizontal{
    margin: 10px 0;
  }
  .upload {
    padding: 10px 40px 0 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .form_title {
      font-family: PingFangSC-Medium, sans-serif;
      text-align: left;
      font-size: 24px;
      margin: 0;
    }
    /deep/ .el-button {
      border: 1px solid #668994;
      background-color: #fff;
      color: #668994;
      font-size: 16px;
    }
    /deep/ .el-button.is-disabled,
    /deep/ .el-button.is-disabled:focus,
    /deep/ .el-button.is-disabled:hover {
      color: #c0c4cc;
      cursor: not-allowed;
      background-image: none;
      background-color: #f5f7fa;
      border: 0.017857rem solid #dcdfe6;
    }
  }
  .tag {
    padding-left: 40px;
    text-align: left;
    /deep/ .el-tag {
      background-color: #fff;
      color: #668994;
      border: none;
      font-size: 20px;
      font-family: PingFangSC-Semibold, sans-serif;
      margin-right: 20px;
      width: 48%;
      .el-tag__close:hover,
      .el-icon-close {
        background-color: #fff;
        color: #668994;
        border: 1px solid #668994;
      }
      &:hover {
        cursor: pointer;
      }
    }
  }
}
</style>